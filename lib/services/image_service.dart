import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image/image.dart' as img;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/watermark_model.dart';
// import '../models/photo_model.dart'; // 暂时不使用
import '../utils/constants.dart';

// 图像处理服务类
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  // 合成带水印的图像
  Future<File> compositeImageWithWatermarks({
    required File originalImage,
    required List<WatermarkModel> watermarks,
    required Size previewSize,
    required Size imageSize,
    Function(double)? onProgress,
  }) async {
    try {
      // 初始进度
      onProgress?.call(0.1);

      // 读取原始图像
      final originalBytes = await originalImage.readAsBytes();
      final originalImg = img.decodeImage(originalBytes);

      if (originalImg == null) {
        throw Exception('无法解码原始图像');
      }

      onProgress?.call(0.2);

      // 创建合成图像
      final compositeImg = img.Image.from(originalImg);

      // 预缓存所有水印图像
      final watermarkImages = <WatermarkModel, img.Image>{};
      for (int i = 0; i < watermarks.length; i++) {
        final watermark = watermarks[i];
        final watermarkImg = await _downloadAndDecodeWatermark(watermark.url);
        if (watermarkImg != null) {
          watermarkImages[watermark] = watermarkImg;
        }
        onProgress?.call(0.2 + (i + 1) / watermarks.length * 0.3);
      }

      onProgress?.call(0.5);

      // 添加每个水印（使用新的所见即所得算法）
      int processedCount = 0;
      for (final watermark in watermarks) {
        final watermarkImg = watermarkImages[watermark];
        if (watermarkImg != null) {
          await _addWatermarkToImageOptimized(
            compositeImg,
            watermark,
            watermarkImg,
            previewSize,
            imageSize,
          );
        }
        processedCount++;
        onProgress?.call(0.5 + processedCount / watermarks.length * 0.4);
      }

      onProgress?.call(0.9);

      // 保存合成图像
      final outputFile = await _saveCompositeImage(compositeImg);

      onProgress?.call(1.0);

      return outputFile;
    } catch (e) {
      throw Exception('图像合成失败: $e');
    }
  }

  // 优化的水印合成方法（所见即所得）
  Future<void> _addWatermarkToImageOptimized(
    img.Image baseImage,
    WatermarkModel watermark,
    img.Image watermarkImg,
    Size previewSize,
    Size imageSize,
  ) async {
    try {
      // 计算实际预览区域尺寸（基于实际图像宽高比，contain模式）
      final actualPreviewSize = _calculateActualPreviewSizeFromImage(
        previewSize,
        imageSize,
      );

      // 计算实际预览到图像的缩放比例（应该X和Y一致）
      final scale = imageSize.width / actualPreviewSize.width; // 使用统一缩放
      final scaleX = scale;
      final scaleY = scale;

      debugPrint('=== 水印合成调试信息 ===');
      debugPrint('预览容器尺寸: ${previewSize.width} x ${previewSize.height}');
      debugPrint(
        '实际预览尺寸: ${actualPreviewSize.width} x ${actualPreviewSize.height}',
      );
      debugPrint('图像尺寸: ${imageSize.width} x ${imageSize.height}');
      debugPrint('缩放比例: scaleX=$scaleX, scaleY=$scaleY');

      // 计算实际预览区域在容器中的偏移（居中显示）
      final previewOffsetX = (previewSize.width - actualPreviewSize.width) / 2;
      final previewOffsetY =
          (previewSize.height - actualPreviewSize.height) / 2;

      debugPrint('预览区域偏移: ($previewOffsetX, $previewOffsetY)');
      debugPrint('水印容器位置: ${watermark.position}');

      // 重新设计：使用统一的容器坐标系统
      // 1. 获取水印在容器中的基础显示尺寸（与DraggableWatermark一致）
      final baseDisplaySize = _calculateBaseDisplaySize(
        watermarkImg,
        previewSize, // 使用容器尺寸，与DraggableWatermark保持一致
      );

      // 2. 计算水印在容器中的中心点坐标
      final containerCenterX =
          watermark.position.dx + (baseDisplaySize.width * watermark.scale) / 2;
      final containerCenterY =
          watermark.position.dy +
          (baseDisplaySize.height * watermark.scale) / 2;

      debugPrint(
        '基础显示尺寸: ${baseDisplaySize.width} x ${baseDisplaySize.height}',
      );
      debugPrint('水印中心点坐标(容器): ($containerCenterX, $containerCenterY)');

      // 3. 计算水印在容器中的相对位置（0-1范围）
      final containerRelativeX = containerCenterX / previewSize.width;
      final containerRelativeY = containerCenterY / previewSize.height;

      debugPrint('容器相对位置: ($containerRelativeX, $containerRelativeY)');

      // 4. 将容器相对位置映射到实际预览区域
      // 考虑预览区域在容器中的偏移
      final previewRelativeX =
          (containerCenterX - previewOffsetX) / actualPreviewSize.width;
      final previewRelativeY =
          (containerCenterY - previewOffsetY) / actualPreviewSize.height;

      debugPrint('预览相对位置: ($previewRelativeX, $previewRelativeY)');

      // 检查水印是否在预览区域内（允许一定的边界容差）
      if (previewRelativeX < -0.5 ||
          previewRelativeX > 1.5 ||
          previewRelativeY < -0.5 ||
          previewRelativeY > 1.5) {
        debugPrint('水印超出预览区域，跳过合成');
        return;
      }

      debugPrint('水印缩放: ${watermark.scale}');
      debugPrint('水印旋转: ${watermark.rotation}');

      // 5. 计算水印在最终图像中的尺寸和位置
      final finalWidth = (baseDisplaySize.width * watermark.scale * scale)
          .round();
      final finalHeight = (baseDisplaySize.height * watermark.scale * scale)
          .round();

      // 使用预览相对位置计算最终图像中的中心点坐标
      final finalCenterX = (previewRelativeX * imageSize.width).round();
      final finalCenterY = (previewRelativeY * imageSize.height).round();

      // 计算最终图像中水印的左上角坐标（从中心点计算）
      final finalX = finalCenterX - (finalWidth / 2).round();
      final finalY = finalCenterY - (finalHeight / 2).round();

      debugPrint('=== 最终坐标转换结果 ===');
      debugPrint('最终中心点: ($finalCenterX, $finalCenterY)');
      debugPrint('最终位置: ($finalX, $finalY)');
      debugPrint('最终尺寸: $finalWidth x $finalHeight');
      debugPrint('图像尺寸: ${imageSize.width} x ${imageSize.height}');

      // 调整水印大小到最终尺寸（保持宽高比）
      final resizedWatermark = img.copyResize(
        watermarkImg,
        width: finalWidth,
        height: finalHeight,
        maintainAspect: true, // 关键：保持宽高比
      );

      debugPrint(
        '调整后水印尺寸: ${resizedWatermark.width} x ${resizedWatermark.height}',
      );

      // 应用旋转
      final rotatedWatermark = watermark.rotation != 0.0
          ? img.copyRotate(resizedWatermark, angle: watermark.rotation)
          : resizedWatermark;

      // 应用透明度
      final watermarkWithOpacity = _applyOpacity(
        rotatedWatermark,
        watermark.opacity,
      );

      // 合成到基础图像
      img.compositeImage(
        baseImage,
        watermarkWithOpacity,
        dstX: finalX,
        dstY: finalY,
      );

      debugPrint('=== 水印合成完成 ===');
    } catch (e) {
      debugPrint('添加水印失败: $e');
    }
  }

  // 计算实际预览区域尺寸（基于实际图像宽高比和contain模式）
  Size _calculateActualPreviewSizeFromImage(
    Size containerSize,
    Size imageSize,
  ) {
    // 使用实际图像的宽高比
    final double imageAspectRatio = imageSize.width / imageSize.height;
    final double containerAspectRatio =
        containerSize.width / containerSize.height;

    double actualWidth, actualHeight;

    if (containerAspectRatio > imageAspectRatio) {
      // 容器更宽，以高度为准
      actualHeight = containerSize.height;
      actualWidth = actualHeight * imageAspectRatio;
    } else {
      // 容器更高，以宽度为准
      actualWidth = containerSize.width;
      actualHeight = actualWidth / imageAspectRatio;
    }

    return Size(actualWidth, actualHeight);
  }

  // 计算水印在预览中的基础显示尺寸（不包含用户缩放）
  Size _calculateBaseDisplaySize(img.Image watermarkImg, Size containerSize) {
    // 获取水印原始尺寸
    final originalSize = Size(
      watermarkImg.width.toDouble(),
      watermarkImg.height.toDouble(),
    );

    // 应用智能缩放逻辑（与DraggableWatermark中的逻辑完全一致）
    double displayWidth = originalSize.width;
    double displayHeight = originalSize.height;

    // 如果水印宽度大于容器宽度，则调整为容器宽度，高度等比缩放
    if (displayWidth > containerSize.width) {
      final widthScale = containerSize.width / displayWidth;
      displayWidth = containerSize.width;
      displayHeight = displayHeight * widthScale;
    }

    // 如果调整后的高度超出显示区域，则以高度为准，宽度等比缩放
    if (displayHeight > containerSize.height) {
      final heightScale = containerSize.height / displayHeight;
      displayHeight = containerSize.height;
      displayWidth = displayWidth * heightScale;
    }

    return Size(displayWidth, displayHeight);
  }

  // 下载并解码水印图像（优化版本）
  Future<img.Image?> _downloadAndDecodeWatermark(String url) async {
    try {
      // 使用cached_network_image的缓存机制
      final imageProvider = CachedNetworkImageProvider(url);
      final imageStream = imageProvider.resolve(const ImageConfiguration());

      // 等待图像加载完成
      final completer = Completer<ui.Image>();
      late ImageStreamListener listener;

      listener = ImageStreamListener((ImageInfo info, bool synchronousCall) {
        completer.complete(info.image);
        imageStream.removeListener(listener);
      });

      imageStream.addListener(listener);
      final uiImage = await completer.future;

      // 转换为字节数据
      final byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // 直接解码为img.Image
      return img.decodeImage(bytes);
    } catch (e) {
      debugPrint('下载水印图像失败: $e');
      return null;
    }
  }

  // 下载水印图像（保留原方法以兼容）
  Future<Uint8List> _downloadWatermarkImage(String url) async {
    try {
      // 使用cached_network_image的缓存机制
      final imageProvider = CachedNetworkImageProvider(url);
      final imageStream = imageProvider.resolve(const ImageConfiguration());

      // 等待图像加载完成
      final completer = Completer<ui.Image>();
      late ImageStreamListener listener;

      listener = ImageStreamListener((ImageInfo info, bool synchronousCall) {
        completer.complete(info.image);
        imageStream.removeListener(listener);
      });

      imageStream.addListener(listener);
      final uiImage = await completer.future;

      // 转换为字节数据
      final byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
      return byteData!.buffer.asUint8List();
    } catch (e) {
      throw Exception('下载水印图像失败: $e');
    }
  }

  // 应用透明度
  img.Image _applyOpacity(img.Image image, double opacity) {
    if (opacity >= 1.0) return image;

    // 简化处理：暂时跳过透明度处理，直接返回原图像
    // 在后续版本中可以添加更复杂的透明度处理
    return image;
  }

  // 保存合成图像
  Future<File> _saveCompositeImage(img.Image image) async {
    final directory = await getApplicationDocumentsDirectory();
    final photoDir = Directory(
      '${directory.path}/${AppConstants.photoDirectory}',
    );

    // 确保目录存在
    if (!await photoDir.exists()) {
      await photoDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = const Uuid().v4().substring(0, 8);
    final fileName = 'watermark_composite_${timestamp}_$uuid.jpg';
    final filePath = '${photoDir.path}/$fileName';

    // 编码为JPEG
    final jpegBytes = img.encodeJpg(image, quality: 95);

    // 写入文件
    final file = File(filePath);
    await file.writeAsBytes(jpegBytes);

    return file;
  }

  // 从Widget创建图像
  Future<ui.Image> captureWidgetAsImage(
    GlobalKey key, {
    double pixelRatio = 1.0,
  }) async {
    try {
      final RenderRepaintBoundary boundary =
          key.currentContext!.findRenderObject() as RenderRepaintBoundary;

      final ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
      return image;
    } catch (e) {
      throw Exception('捕获Widget图像失败: $e');
    }
  }

  // 将ui.Image转换为File
  Future<File> saveUiImageAsFile(
    ui.Image image, {
    String? fileName,
    int quality = 95,
  }) async {
    try {
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // 如果需要JPEG格式，进行转换
      final img.Image? decodedImage = img.decodePng(bytes);
      if (decodedImage == null) {
        throw Exception('无法解码图像');
      }

      final jpegBytes = img.encodeJpg(decodedImage, quality: quality);

      // 保存文件
      final directory = await getApplicationDocumentsDirectory();
      final photoDir = Directory(
        '${directory.path}/${AppConstants.photoDirectory}',
      );

      if (!await photoDir.exists()) {
        await photoDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uuid = const Uuid().v4().substring(0, 8);
      final finalFileName = fileName ?? 'captured_${timestamp}_$uuid.jpg';
      final filePath = '${photoDir.path}/$finalFileName';

      final file = File(filePath);
      await file.writeAsBytes(jpegBytes);

      return file;
    } catch (e) {
      throw Exception('保存图像文件失败: $e');
    }
  }

  // 获取图像尺寸
  Future<Size> getImageSize(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      return Size(image.width.toDouble(), image.height.toDouble());
    } catch (e) {
      throw Exception('获取图像尺寸失败: $e');
    }
  }

  // 压缩图像
  Future<File> compressImage(
    File imageFile, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      img.Image processedImage = image;

      // 调整尺寸
      if (maxWidth != null || maxHeight != null) {
        processedImage = img.copyResize(
          processedImage,
          width: maxWidth,
          height: maxHeight,
          maintainAspect: true,
        );
      }

      // 压缩
      final compressedBytes = img.encodeJpg(processedImage, quality: quality);

      // 保存压缩后的文件
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory(
        '${directory.path}/${AppConstants.tempDirectory}',
      );

      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'compressed_$timestamp.jpg';
      final filePath = '${tempDir.path}/$fileName';

      final compressedFile = File(filePath);
      await compressedFile.writeAsBytes(compressedBytes);

      return compressedFile;
    } catch (e) {
      throw Exception('压缩图像失败: $e');
    }
  }

  // 创建缩略图
  Future<File> createThumbnail(
    File imageFile, {
    int size = 200,
    int quality = 80,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      // 创建正方形缩略图
      final thumbnail = img.copyResizeCropSquare(image, size: size);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: quality);

      // 保存缩略图
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory(
        '${directory.path}/${AppConstants.tempDirectory}',
      );

      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'thumbnail_$timestamp.jpg';
      final filePath = '${tempDir.path}/$fileName';

      final thumbnailFile = File(filePath);
      await thumbnailFile.writeAsBytes(thumbnailBytes);

      return thumbnailFile;
    } catch (e) {
      throw Exception('创建缩略图失败: $e');
    }
  }
}
